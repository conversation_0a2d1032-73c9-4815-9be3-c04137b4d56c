"""
智能模板路由器
根据模板文件扩展名自动选择渲染引擎
"""
import os
import logging
from django.shortcuts import render
from django.http import HttpResponse
from django.conf import settings
from django.template.loader import get_template
from django.template.exceptions import TemplateDoesNotExist

from .vue_renderer import VueTemplateRenderer
from .context_processor import UnifiedContextProcessor

logger = logging.getLogger(__name__)


class TemplateRouter:
    """智能模板路由器"""
    
    def __init__(self):
        self.vue_renderer = VueTemplateRenderer()
        self.context_processor = UnifiedContextProcessor()
        
    def render(self, request, template_name, context=None, content_type=None, status=None, using=None):

        try:
            # 检测模板类型
            if self._is_vue_template(template_name):
                return self._render_vue_template(request, template_name, context, content_type, status)
            else:
                return self._render_html_template(request, template_name, context, content_type, status, using)
                
        except Exception as e:
            logger.error(f"模板渲染失败: {template_name} - {str(e)}")
            return self._render_error_response(request, template_name, str(e))
    
    def _is_vue_template(self, template_name):
        """检测是否为Vue模板"""
        return template_name.endswith('.vue')
    
    def _render_html_template(self, request, template_name, context, content_type, status, using):
        """渲染HTML模板（Django原生方式）"""
        try:
            # 使用Django原生render函数
            return render(
                request=request,
                template_name=template_name,
                context=context,
                content_type=content_type,
                status=status,
                using=using
            )
        except TemplateDoesNotExist:
            logger.warning(f"HTML模板不存在: {template_name}")
            raise
        except Exception as e:
            logger.error(f"HTML模板渲染失败: {template_name} - {str(e)}")
            raise
    
    def _render_vue_template(self, request, template_name, context, content_type, status):
        """渲染Vue模板"""
        try:
            # 处理上下文数据
            processed_context = self.context_processor.process_context(
                request=request,
                context=context,
                template_type='vue'
            )
            
            # 使用Vue渲染器
            rendered_content = self.vue_renderer.render(
                request=request,
                template_name=template_name,
                context=processed_context
            )
            
            # 创建HTTP响应
            response = HttpResponse(
                content=rendered_content,
                content_type=content_type or 'text/html',
                status=status or 200
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Vue模板渲染失败: {template_name} - {str(e)}")
            raise
    
    def _render_error_response(self, request, template_name, error_message):
        """渲染错误响应"""
        # 记录错误但避免重复日志
        if not hasattr(request, '_template_error_logged'):
            logger.error(f"模板渲染失败: {template_name} - {str(error_message)}")
            request._template_error_logged = True
        
        if settings.DEBUG:
            # 开发环境：显示详细错误信息
            error_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>模板渲染错误</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    .error {{ background: #ffebee; border: 1px solid #f44336; padding: 20px; border-radius: 5px; }}
                    .error h1 {{ color: #f44336; margin-top: 0; }}
                    .error pre {{ background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }}
                </style>
            </head>
            <body>
                <div class="error">
                    <h1>模板渲染错误</h1>
                    <p><strong>模板文件:</strong> {template_name}</p>
                    <p><strong>错误信息:</strong></p>
                    <pre>{error_message}</pre>
                    <p><strong>提示:</strong> 请检查模板文件是否存在以及路径是否正确</p>
                </div>
            </body>
            </html>
            """
            return HttpResponse(error_html, status=500)
        else:
            # 生产环境：显示友好错误页面
            try:
                return render(request, '500.html', {'error': '页面渲染失败'}, status=500)
            except TemplateDoesNotExist:
                return HttpResponse('服务器内部错误', status=500)


# 全局模板路由器实例
template_router = TemplateRouter()


def smart_render(request, template_name, context=None, content_type=None, status=None, using=None):

    return template_router.render(
        request=request,
        template_name=template_name,
        context=context,
        content_type=content_type,
        status=status,
        using=using
    )